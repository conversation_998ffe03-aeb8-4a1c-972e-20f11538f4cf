'use client';

import React, { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';
import { KnowledgeGraph, ConceptNode, ConceptEdge, VisualizationConfig } from '@/types/knowledge-graph';
import { getCategoryColor, getEdgeStyle, calculateNodeSize } from '@/utils/graph-utils';

interface KnowledgeGraphVisualizationProps {
  graph: KnowledgeGraph;
  config?: Partial<VisualizationConfig>;
  onNodeClick?: (node: ConceptNode) => void;
  onNodeHover?: (node: ConceptNode | null) => void;
  className?: string;
}

const defaultConfig: VisualizationConfig = {
  width: 800,
  height: 600,
  nodeSize: { min: 8, max: 24 },
  colors: {} as any, // 将在组件中填充
  layout: 'force',
  showLabels: true,
  showEdgeLabels: false,
  animationDuration: 300,
  zoomExtent: [0.1, 3]
};

export default function KnowledgeGraphVisualization({
  graph,
  config = {},
  onNodeClick,
  onNodeHover,
  className = ''
}: KnowledgeGraphVisualizationProps) {
  const svgRef = useRef<SVGSVGElement>(null);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [hoveredNode, setHoveredNode] = useState<string | null>(null);

  const finalConfig = { ...defaultConfig, ...config };

  useEffect(() => {
    if (!svgRef.current || !graph.nodes.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove(); // 清除之前的内容

    const { width, height } = finalConfig;

    // 设置SVG尺寸
    svg.attr('width', width).attr('height', height);

    // 创建缩放行为，优化移动端体验
    const zoom = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent(finalConfig.zoomExtent)
      .filter((event) => {
        // 允许触摸缩放和鼠标滚轮缩放
        return !event.ctrlKey && !event.button;
      })
      .on('zoom', (event) => {
        container.attr('transform', event.transform);
      });

    svg.call(zoom);

    // 创建主容器
    const container = svg.append('g');

    // 创建力导向布局
    const simulation = d3.forceSimulation<ConceptNode>(graph.nodes)
      .force('link', d3.forceLink<ConceptNode, ConceptEdge>(graph.edges)
        .id(d => d.id)
        .distance(d => 100 - d.strength * 50)
        .strength(d => d.strength * 0.5)
      )
      .force('charge', d3.forceManyBody().strength(-300))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius(d => calculateNodeSize((d as ConceptNode).importance) + 5));

    // 创建箭头标记
    const defs = svg.append('defs');
    
    defs.selectAll('marker')
      .data(['arrow'])
      .enter()
      .append('marker')
      .attr('id', 'arrow')
      .attr('viewBox', '0 -5 10 10')
      .attr('refX', 15)
      .attr('refY', 0)
      .attr('markerWidth', 6)
      .attr('markerHeight', 6)
      .attr('orient', 'auto')
      .append('path')
      .attr('d', 'M0,-5L10,0L0,5')
      .attr('fill', '#999');

    // 绘制边
    const links = container.selectAll('.link')
      .data(graph.edges)
      .enter()
      .append('line')
      .attr('class', 'link')
      .attr('stroke', d => getEdgeStyle(d.type).color)
      .attr('stroke-width', d => getEdgeStyle(d.type).width)
      .attr('stroke-dasharray', d => getEdgeStyle(d.type).dashArray || 'none')
      .attr('marker-end', 'url(#arrow)')
      .attr('opacity', 0.6);

    // 绘制节点
    const nodes = container.selectAll('.node')
      .data(graph.nodes)
      .enter()
      .append('g')
      .attr('class', 'node')
      .style('cursor', 'pointer');

    // 节点圆圈
    nodes.append('circle')
      .attr('r', d => calculateNodeSize(d.importance, finalConfig.nodeSize.min, finalConfig.nodeSize.max))
      .attr('fill', d => getCategoryColor(d.category))
      .attr('stroke', '#fff')
      .attr('stroke-width', 2)
      .attr('opacity', 0.8);

    // 节点标签
    if (finalConfig.showLabels) {
      nodes.append('text')
        .text(d => d.name)
        .attr('dx', d => calculateNodeSize(d.importance) + 8)
        .attr('dy', 4)
        .style('font-size', '12px')
        .style('font-family', 'Arial, sans-serif')
        .style('fill', '#333')
        .style('pointer-events', 'none');
    }

    // 节点交互事件，优化移动端体验
    nodes
      .on('click', (event, d) => {
        event.stopPropagation();
        setSelectedNode(d.id);
        onNodeClick?.(d);
      })
      .on('touchstart', (event, d) => {
        event.preventDefault();
        event.stopPropagation();
        setHoveredNode(d.id);
        onNodeHover?.(d);
        highlightConnectedElements(d.id);
      })
      .on('touchend', (event, d) => {
        event.preventDefault();
        event.stopPropagation();
        setSelectedNode(d.id);
        onNodeClick?.(d);
        setTimeout(() => {
          setHoveredNode(null);
          onNodeHover?.(null);
          resetHighlight();
        }, 2000); // 2秒后自动取消高亮
      })
      .on('mouseenter', (event, d) => {
        // 只在非触摸设备上响应鼠标事件
        if (!('ontouchstart' in window)) {
          setHoveredNode(d.id);
          onNodeHover?.(d);
          highlightConnectedElements(d.id);
        }
      })
      .on('mouseleave', () => {
        // 只在非触摸设备上响应鼠标事件
        if (!('ontouchstart' in window)) {
          setHoveredNode(null);
          onNodeHover?.(null);
          resetHighlight();
        }
      });

    // 拖拽行为，优化移动端体验
    const drag = d3.drag<SVGGElement, ConceptNode>()
      .filter((event) => {
        // 在移动设备上，只允许单指拖拽
        return !event.ctrlKey && !event.button;
      })
      .on('start', (event, d) => {
        if (!event.active) simulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;

        // 在拖拽开始时取消高亮，避免干扰
        resetHighlight();
      })
      .on('drag', (event, d) => {
        d.fx = event.x;
        d.fy = event.y;
      })
      .on('end', (event, d) => {
        if (!event.active) simulation.alphaTarget(0);
        d.fx = null;
        d.fy = null;
      });

    nodes.call(drag);

    // 更新位置
    simulation.on('tick', () => {
      links
        .attr('x1', d => {
          const source = typeof d.source === 'string' ? graph.nodes.find(n => n.id === d.source) : d.source;
          return source?.x || 0;
        })
        .attr('y1', d => {
          const source = typeof d.source === 'string' ? graph.nodes.find(n => n.id === d.source) : d.source;
          return source?.y || 0;
        })
        .attr('x2', d => {
          const target = typeof d.target === 'string' ? graph.nodes.find(n => n.id === d.target) : d.target;
          return target?.x || 0;
        })
        .attr('y2', d => {
          const target = typeof d.target === 'string' ? graph.nodes.find(n => n.id === d.target) : d.target;
          return target?.y || 0;
        });

      nodes.attr('transform', d => `translate(${d.x},${d.y})`);
    });

    // 高亮连接的元素
    function highlightConnectedElements(nodeId: string) {
      const connectedEdges = graph.edges.filter(e => e.source === nodeId || e.target === nodeId);
      const connectedNodeIds = new Set([
        nodeId,
        ...connectedEdges.map(e => e.source),
        ...connectedEdges.map(e => e.target)
      ]);

      // 降低非连接节点的透明度
      nodes.select('circle')
        .transition()
        .duration(200)
        .attr('opacity', d => connectedNodeIds.has(d.id) ? 1 : 0.3);

      nodes.select('text')
        .transition()
        .duration(200)
        .attr('opacity', d => connectedNodeIds.has(d.id) ? 1 : 0.3);

      // 高亮连接的边
      links
        .transition()
        .duration(200)
        .attr('opacity', d => connectedEdges.includes(d) ? 1 : 0.1)
        .attr('stroke-width', d => connectedEdges.includes(d) ? getEdgeStyle(d.type).width * 1.5 : getEdgeStyle(d.type).width);
    }

    // 重置高亮
    function resetHighlight() {
      nodes.select('circle')
        .transition()
        .duration(200)
        .attr('opacity', 0.8);

      nodes.select('text')
        .transition()
        .duration(200)
        .attr('opacity', 1);

      links
        .transition()
        .duration(200)
        .attr('opacity', 0.6)
        .attr('stroke-width', d => getEdgeStyle(d.type).width);
    }

    // 点击空白区域取消选择
    svg.on('click', () => {
      setSelectedNode(null);
    });

    // 清理函数
    return () => {
      simulation.stop();
    };

  }, [graph, finalConfig, onNodeClick, onNodeHover]);

  // 更新选中状态的样式
  useEffect(() => {
    if (!svgRef.current) return;

    const svg = d3.select(svgRef.current);
    
    svg.selectAll('.node circle')
      .attr('stroke', (d: any) => d.id === selectedNode ? '#ff6b6b' : '#fff')
      .attr('stroke-width', (d: any) => d.id === selectedNode ? 3 : 2);

  }, [selectedNode]);

  return (
    <div className={`knowledge-graph-container ${className}`}>
      <svg
        ref={svgRef}
        className="knowledge-graph-svg"
        style={{
          border: '1px solid #e5e7eb',
          borderRadius: '8px',
          background: '#fafafa'
        }}
      />
      
      {/* 图例 */}
      <div className="absolute top-4 right-4 bg-white p-3 rounded-lg shadow-md">
        <h4 className="text-sm font-semibold mb-2">图例</h4>
        <div className="space-y-1 text-xs">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
            <span>概念节点</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-0.5 bg-gray-600"></div>
            <span>关系连线</span>
          </div>
          <div className="text-gray-500">
            节点大小表示重要程度
          </div>
        </div>
      </div>

      {/* 控制面板 */}
      <div className="absolute bottom-4 left-4 bg-white p-3 rounded-lg shadow-md">
        <div className="flex gap-2">
          <button
            onClick={() => {
              // 简单的重置视图功能
              if (svgRef.current) {
                const svg = d3.select(svgRef.current);
                const container = svg.select('g');
                container.attr('transform', 'translate(0,0) scale(1)');
              }
            }}
            className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded"
          >
            重置视图
          </button>
        </div>
      </div>
    </div>
  );
}
