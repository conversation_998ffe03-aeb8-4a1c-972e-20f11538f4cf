'use client';

import React from 'react';
import { ConceptNode } from '@/types/knowledge-graph';
import { 
  X, 
  Lightbulb, 
  Map, 
  Building, 
  Palette, 
  Key, 
  AlertTriangle, 
  Target, 
  MessageCircle,
  Star,
  Zap,
  BookOpen,
  Users
} from 'lucide-react';

interface DetailedConceptAnalysisProps {
  node: ConceptNode | null;
  isOpen: boolean;
  onClose: () => void;
  coreInsight?: string;
  conceptMap?: string;
  actionGuide?: {
    beginner: string[];
    advanced: string[];
  };
  mentorMessage?: string;
}

export default function DetailedConceptAnalysis({
  node,
  isOpen,
  onClose,
  coreInsight,
  conceptMap,
  actionGuide,
  mentorMessage
}: DetailedConceptAnalysisProps) {
  if (!isOpen || !node) {
    return null;
  }

  const getConceptTypeIcon = (type?: string) => {
    switch (type) {
      case '承重墙':
        return <Building className="w-5 h-5 text-red-500" />;
      case '装饰品':
        return <Palette className="w-5 h-5 text-blue-500" />;
      case '暗门':
        return <Key className="w-5 h-5 text-purple-500" />;
      default:
        return <Star className="w-5 h-5 text-gray-500" />;
    }
  };

  const getConceptTypeColor = (type?: string) => {
    switch (type) {
      case '承重墙':
        return 'bg-red-50 border-red-200 text-red-800';
      case '装饰品':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      case '暗门':
        return 'bg-purple-50 border-purple-200 text-purple-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* 头部 */}
        <div className="sticky top-0 bg-white border-b border-gray-200 p-4 sm:p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getConceptTypeIcon(node.conceptType)}
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {node.name} - 深度解析
                </h1>
                {node.conceptType && (
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getConceptTypeColor(node.conceptType)}`}>
                    {node.conceptType}概念
                  </span>
                )}
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X className="w-6 h-6 text-gray-500" />
            </button>
          </div>
        </div>

        {/* 内容 */}
        <div className="p-4 sm:p-6 space-y-8">
          {/* 核心洞察 */}
          {(coreInsight || node.essence) && (
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-3">
                <Lightbulb className="w-5 h-5 text-blue-600" />
                <h2 className="text-lg font-semibold text-blue-900">💡 一句话洞察</h2>
              </div>
              <p className="text-blue-800 text-lg italic">
                {coreInsight || node.essence}
              </p>
            </div>
          )}

          {/* 概念地图 */}
          {conceptMap && (
            <div>
              <div className="flex items-center gap-2 mb-4">
                <Map className="w-5 h-5 text-green-600" />
                <h2 className="text-xl font-semibold text-gray-900">🗺️ 概念地图</h2>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <p className="text-green-800">{conceptMap}</p>
              </div>
            </div>
          )}

          {/* 核心支柱 */}
          {node.conceptType === '承重墙' && (
            <div>
              <div className="flex items-center gap-2 mb-4">
                <Building className="w-5 h-5 text-red-600" />
                <h2 className="text-xl font-semibold text-gray-900">🏗️ 核心支柱（承重墙）</h2>
              </div>
              
              <div className="space-y-6">
                {/* 本质理解 */}
                {node.essence && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">本质理解</h3>
                    <p className="text-gray-700 leading-relaxed">{node.essence}</p>
                  </div>
                )}

                {/* 为何关键 */}
                {node.significance && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">为何关键</h3>
                    <p className="text-gray-700 leading-relaxed">{node.significance}</p>
                  </div>
                )}

                {/* 实战指南 */}
                {node.practice && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">实战指南</h3>
                    <p className="text-gray-700 leading-relaxed">{node.practice}</p>
                  </div>
                )}

                {/* 常见陷阱 */}
                {node.pitfalls && node.pitfalls.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                      <AlertTriangle className="w-5 h-5 text-orange-500" />
                      常见陷阱
                    </h3>
                    <div className="space-y-2">
                      {node.pitfalls.map((pitfall, index) => (
                        <div key={index} className="flex items-start gap-2 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                          <span className="text-orange-500 font-bold">⚠️</span>
                          <span className="text-orange-800">{pitfall}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 锦上添花 */}
          {node.conceptType === '装饰品' && (
            <div>
              <div className="flex items-center gap-2 mb-4">
                <Palette className="w-5 h-5 text-blue-600" />
                <h2 className="text-xl font-semibold text-gray-900">🎭 锦上添花（装饰品）</h2>
              </div>
              
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-blue-800 leading-relaxed">{node.description}</p>
                {node.significance && (
                  <div className="mt-3 pt-3 border-t border-blue-200">
                    <p className="text-blue-700 text-sm">
                      <strong>与核心概念的关联：</strong>{node.significance}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 高手秘籍 */}
          {(node.conceptType === '暗门' || (node.secretTips && node.secretTips.length > 0)) && (
            <div>
              <div className="flex items-center gap-2 mb-4">
                <Key className="w-5 h-5 text-purple-600" />
                <h2 className="text-xl font-semibold text-gray-900">🗝️ 高手秘籍（暗门）</h2>
              </div>
              
              {node.secretTips && node.secretTips.length > 0 ? (
                <div className="space-y-4">
                  {node.secretTips.map((tip, index) => (
                    <div key={index} className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                      <div className="flex items-center gap-2 mb-3">
                        <Zap className="w-4 h-4 text-purple-600" />
                        <h3 className="font-semibold text-purple-900">💫 秘技：{tip.name}</h3>
                      </div>
                      <div className="space-y-2 text-sm">
                        <p><strong className="text-purple-800">场景：</strong><span className="text-purple-700">{tip.scenario}</span></p>
                        <p><strong className="text-purple-800">妙处：</strong><span className="text-purple-700">{tip.advantage}</span></p>
                        <p><strong className="text-purple-800">操作：</strong><span className="text-purple-700">{tip.operation}</span></p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <p className="text-purple-800 leading-relaxed">{node.description}</p>
                </div>
              )}
            </div>
          )}

          {/* 相关哲学家 */}
          {node.metadata.philosophers.length > 0 && (
            <div>
              <div className="flex items-center gap-2 mb-4">
                <Users className="w-5 h-5 text-indigo-600" />
                <h2 className="text-xl font-semibold text-gray-900">👥 相关哲学家</h2>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {node.metadata.philosophers.map((philosopher) => (
                  <div key={philosopher.id} className="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <h3 className="font-semibold text-indigo-900">{philosopher.name}</h3>
                    {philosopher.mainContributions.length > 0 && (
                      <div className="mt-2">
                        <p className="text-xs text-indigo-600 mb-1">主要贡献：</p>
                        <ul className="text-sm text-indigo-800 space-y-1">
                          {philosopher.mainContributions.map((contribution, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <span className="text-indigo-400 mt-1">•</span>
                              <span>{contribution}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 行动指南 */}
          {actionGuide && (
            <div>
              <div className="flex items-center gap-2 mb-4">
                <Target className="w-5 h-5 text-green-600" />
                <h2 className="text-xl font-semibold text-gray-900">🎯 行动指南</h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* 新手指南 */}
                {actionGuide.beginner && actionGuide.beginner.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-green-900 mb-3">如果你是新手</h3>
                    <ol className="space-y-2">
                      {actionGuide.beginner.map((item, index) => (
                        <li key={index} className="flex items-start gap-3">
                          <span className="flex-shrink-0 w-6 h-6 bg-green-100 text-green-800 rounded-full flex items-center justify-center text-sm font-semibold">
                            {index + 1}
                          </span>
                          <span className="text-gray-700">{item}</span>
                        </li>
                      ))}
                    </ol>
                  </div>
                )}

                {/* 进阶指南 */}
                {actionGuide.advanced && actionGuide.advanced.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-blue-900 mb-3">如果你想进阶</h3>
                    <ul className="space-y-2">
                      {actionGuide.advanced.map((item, index) => (
                        <li key={index} className="flex items-start gap-3">
                          <span className="text-blue-500 mt-1">▶</span>
                          <span className="text-gray-700">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 导师寄语 */}
          {mentorMessage && (
            <div>
              <div className="flex items-center gap-2 mb-4">
                <MessageCircle className="w-5 h-5 text-amber-600" />
                <h2 className="text-xl font-semibold text-gray-900">💭 导师寄语</h2>
              </div>
              
              <div className="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg p-6">
                <p className="text-amber-900 leading-relaxed text-lg italic">
                  {mentorMessage}
                </p>
              </div>
            </div>
          )}

          {/* 记住提示 */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <BookOpen className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-600">记住</span>
            </div>
            <p className="text-gray-700 text-sm italic">
              真正的掌握不在于知道所有细节，而在于理解核心逻辑，并能在实践中灵活运用。
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
