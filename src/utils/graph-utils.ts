import { 
  ConceptNode, 
  ConceptEdge, 
  KnowledgeGraph, 
  PhilosophyCategory,
  RelationType,
  SearchFilters,
  SearchResult 
} from '@/types/knowledge-graph';

// 图谱数据处理工具函数

/**
 * 根据重要性计算节点大小
 */
export function calculateNodeSize(importance: number, minSize = 8, maxSize = 24): number {
  return minSize + (maxSize - minSize) * importance;
}

/**
 * 根据哲学分类获取颜色
 */
export function getCategoryColor(category: PhilosophyCategory): string {
  const colorMap: Record<PhilosophyCategory, string> = {
    [PhilosophyCategory.METAPHYSICS]: '#8B5CF6', // 紫色
    [PhilosophyCategory.EPISTEMOLOGY]: '#3B82F6', // 蓝色
    [PhilosophyCategory.ETHICS]: '#EF4444', // 红色
    [PhilosophyCategory.LOGIC]: '#10B981', // 绿色
    [PhilosophyCategory.AESTHETICS]: '#F59E0B', // 橙色
    [PhilosophyCategory.POLITICAL_PHILOSOPHY]: '#6366F1', // 靛蓝
    [PhilosophyCategory.PHILOSOPHY_OF_MIND]: '#EC4899', // 粉色
    [PhilosophyCategory.PHILOSOPHY_OF_RELIGION]: '#8B5A2B', // 棕色
    [PhilosophyCategory.EXISTENTIALISM]: '#1F2937', // 深灰
    [PhilosophyCategory.PHENOMENOLOGY]: '#7C3AED', // 深紫
    [PhilosophyCategory.ANALYTIC_PHILOSOPHY]: '#059669', // 深绿
    [PhilosophyCategory.CONTINENTAL_PHILOSOPHY]: '#DC2626', // 深红
    [PhilosophyCategory.EASTERN_PHILOSOPHY]: '#D97706', // 深橙
    [PhilosophyCategory.ANCIENT_PHILOSOPHY]: '#92400E', // 古铜色
    [PhilosophyCategory.MODERN_PHILOSOPHY]: '#1E40AF', // 深蓝
    [PhilosophyCategory.CONTEMPORARY_PHILOSOPHY]: '#7C2D12' // 深棕
  };
  return colorMap[category] || '#6B7280';
}

/**
 * 根据关系类型获取边的样式
 */
export function getEdgeStyle(type: RelationType): { color: string; dashArray?: string; width: number } {
  const styleMap: Record<RelationType, { color: string; dashArray?: string; width: number }> = {
    [RelationType.CONTAINS]: { color: '#374151', width: 2 },
    [RelationType.INFLUENCES]: { color: '#3B82F6', width: 1.5 },
    [RelationType.OPPOSES]: { color: '#EF4444', width: 1.5, dashArray: '5,5' },
    [RelationType.DEVELOPS]: { color: '#10B981', width: 2 },
    [RelationType.APPLIES]: { color: '#F59E0B', width: 1 },
    [RelationType.RELATES]: { color: '#6B7280', width: 1 },
    [RelationType.DERIVES]: { color: '#8B5CF6', width: 1.5 },
    [RelationType.CRITIQUES]: { color: '#DC2626', width: 1.5, dashArray: '3,3' },
    [RelationType.SYNTHESIZES]: { color: '#059669', width: 2 },
    [RelationType.EXEMPLIFIES]: { color: '#D97706', width: 1 }
  };
  return styleMap[type] || { color: '#6B7280', width: 1 };
}

/**
 * 计算图谱的层级布局
 */
export function calculateHierarchicalLayout(
  nodes: ConceptNode[], 
  edges: ConceptEdge[],
  width: number,
  height: number
): ConceptNode[] {
  // 按层级分组
  const levelGroups: { [level: number]: ConceptNode[] } = {};
  nodes.forEach(node => {
    if (!levelGroups[node.level]) {
      levelGroups[node.level] = [];
    }
    levelGroups[node.level].push(node);
  });

  const levels = Object.keys(levelGroups).map(Number).sort((a, b) => a - b);
  const levelHeight = height / (levels.length + 1);

  return nodes.map(node => {
    const levelNodes = levelGroups[node.level];
    const nodeIndex = levelNodes.indexOf(node);
    const levelWidth = width / (levelNodes.length + 1);

    return {
      ...node,
      position: {
        x: levelWidth * (nodeIndex + 1),
        y: levelHeight * (node.level + 1)
      }
    };
  });
}

/**
 * 搜索和过滤节点
 */
export function searchNodes(
  graph: KnowledgeGraph,
  query: string,
  filters: SearchFilters = {}
): SearchResult {
  let filteredNodes = graph.nodes;

  // 文本搜索
  if (query.trim()) {
    const searchTerm = query.toLowerCase();
    filteredNodes = filteredNodes.filter(node =>
      node.name.toLowerCase().includes(searchTerm) ||
      node.description.toLowerCase().includes(searchTerm) ||
      node.metadata.keywords.some(keyword => 
        keyword.toLowerCase().includes(searchTerm)
      )
    );
  }

  // 分类过滤
  if (filters.categories && filters.categories.length > 0) {
    filteredNodes = filteredNodes.filter(node =>
      filters.categories!.includes(node.category)
    );
  }

  // 难度过滤
  if (filters.difficulty && filters.difficulty.length > 0) {
    filteredNodes = filteredNodes.filter(node =>
      filters.difficulty!.includes(node.metadata.difficulty)
    );
  }

  // 哲学家过滤
  if (filters.philosophers && filters.philosophers.length > 0) {
    filteredNodes = filteredNodes.filter(node =>
      node.metadata.philosophers.some(philosopher =>
        filters.philosophers!.includes(philosopher.name)
      )
    );
  }

  // 关键词过滤
  if (filters.keywords && filters.keywords.length > 0) {
    filteredNodes = filteredNodes.filter(node =>
      filters.keywords!.some(keyword =>
        node.metadata.keywords.includes(keyword)
      )
    );
  }

  // 获取相关的边
  const nodeIds = new Set(filteredNodes.map(node => node.id));
  const filteredEdges = graph.edges.filter(edge => {
    const sourceId = typeof edge.source === 'string' ? edge.source : edge.source.id;
    const targetId = typeof edge.target === 'string' ? edge.target : edge.target.id;
    return nodeIds.has(sourceId) && nodeIds.has(targetId);
  });

  return {
    nodes: filteredNodes,
    edges: filteredEdges,
    totalCount: filteredNodes.length,
    query,
    filters
  };
}

/**
 * 获取节点的邻居节点
 */
export function getNeighborNodes(
  nodeId: string,
  graph: KnowledgeGraph,
  depth: number = 1
): { nodes: ConceptNode[]; edges: ConceptEdge[] } {
  const visitedNodes = new Set<string>();
  const resultNodes: ConceptNode[] = [];
  const resultEdges: ConceptEdge[] = [];

  function traverse(currentNodeId: string, currentDepth: number) {
    if (currentDepth > depth || visitedNodes.has(currentNodeId)) {
      return;
    }

    visitedNodes.add(currentNodeId);
    const currentNode = graph.nodes.find(n => n.id === currentNodeId);
    if (currentNode) {
      resultNodes.push(currentNode);
    }

    // 找到相关的边
    const relatedEdges = graph.edges.filter(edge => {
      const sourceId = typeof edge.source === 'string' ? edge.source : edge.source.id;
      const targetId = typeof edge.target === 'string' ? edge.target : edge.target.id;
      return sourceId === currentNodeId || targetId === currentNodeId;
    });

    relatedEdges.forEach(edge => {
      resultEdges.push(edge);
      const sourceId = typeof edge.source === 'string' ? edge.source : edge.source.id;
      const targetId = typeof edge.target === 'string' ? edge.target : edge.target.id;
      const nextNodeId = sourceId === currentNodeId ? targetId : sourceId;
      traverse(nextNodeId, currentDepth + 1);
    });
  }

  traverse(nodeId, 0);

  return {
    nodes: resultNodes,
    edges: resultEdges
  };
}

/**
 * 计算图谱统计信息
 */
export function calculateGraphStats(graph: KnowledgeGraph) {
  const categories = [...new Set(graph.nodes.map(node => node.category))];
  const maxLevel = Math.max(...graph.nodes.map(node => node.level));
  const avgImportance = graph.nodes.reduce((sum, node) => sum + node.importance, 0) / graph.nodes.length;
  
  const relationshipTypes = [...new Set(graph.edges.map(edge => edge.type))];
  const avgRelationshipStrength = graph.edges.reduce((sum, edge) => sum + edge.strength, 0) / graph.edges.length;

  return {
    totalNodes: graph.nodes.length,
    totalEdges: graph.edges.length,
    categories: categories.length,
    maxDepth: maxLevel,
    avgImportance: Math.round(avgImportance * 100) / 100,
    relationshipTypes: relationshipTypes.length,
    avgRelationshipStrength: Math.round(avgRelationshipStrength * 100) / 100,
    density: (2 * graph.edges.length) / (graph.nodes.length * (graph.nodes.length - 1))
  };
}

/**
 * 验证图谱数据完整性
 */
export function validateGraph(graph: KnowledgeGraph): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // 检查节点
  const nodeIds = new Set(graph.nodes.map(node => node.id));
  if (nodeIds.size !== graph.nodes.length) {
    errors.push('存在重复的节点ID');
  }

  // 检查边的引用
  graph.edges.forEach((edge, index) => {
    const sourceId = typeof edge.source === 'string' ? edge.source : edge.source.id;
    const targetId = typeof edge.target === 'string' ? edge.target : edge.target.id;

    if (!nodeIds.has(sourceId)) {
      errors.push(`边 ${index} 的源节点 ${sourceId} 不存在`);
    }
    if (!nodeIds.has(targetId)) {
      errors.push(`边 ${index} 的目标节点 ${targetId} 不存在`);
    }
  });

  // 检查数据范围
  graph.nodes.forEach((node, index) => {
    if (node.importance < 0 || node.importance > 1) {
      errors.push(`节点 ${index} 的重要性值超出范围 [0,1]`);
    }
    if (node.level < 0) {
      errors.push(`节点 ${index} 的层级值不能为负数`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}
