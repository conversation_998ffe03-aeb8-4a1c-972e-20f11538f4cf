// 哲学知识图谱数据模型定义

// 高手秘籍
export interface SecretTip {
  name: string;
  scenario: string; // 使用场景
  advantage: string; // 优势
  operation: string; // 操作方法
}

// 概念节点类型
export interface ConceptNode {
  id: string;
  name: string;
  description: string;
  category: PhilosophyCategory;
  importance: number; // 0-1，影响节点大小
  level: number; // 层级深度，0为根概念
  position?: {
    x: number;
    y: number;
  };
  metadata: ConceptMetadata;
  // D3.js 力导向布局需要的属性
  x?: number;
  y?: number;
  fx?: number | null;
  fy?: number | null;
  vx?: number;
  vy?: number;
  // 新增详细解析字段
  type?: 'core' | 'supporting' | 'advanced'; // 概念类型
  conceptType?: '承重墙' | '装饰品' | '暗门'; // 概念分类
  essence?: string; // 本质理解
  significance?: string; // 为何关键
  practice?: string; // 实战指南
  pitfalls?: string[]; // 常见陷阱
  secretTips?: SecretTip[]; // 高手秘籍
}

// 哲学分类
export enum PhilosophyCategory {
  METAPHYSICS = 'metaphysics', // 形而上学
  EPISTEMOLOGY = 'epistemology', // 认识论
  ETHICS = 'ethics', // 伦理学
  LOGIC = 'logic', // 逻辑学
  AESTHETICS = 'aesthetics', // 美学
  POLITICAL_PHILOSOPHY = 'political_philosophy', // 政治哲学
  PHILOSOPHY_OF_MIND = 'philosophy_of_mind', // 心灵哲学
  PHILOSOPHY_OF_RELIGION = 'philosophy_of_religion', // 宗教哲学
  EXISTENTIALISM = 'existentialism', // 存在主义
  PHENOMENOLOGY = 'phenomenology', // 现象学
  ANALYTIC_PHILOSOPHY = 'analytic_philosophy', // 分析哲学
  CONTINENTAL_PHILOSOPHY = 'continental_philosophy', // 大陆哲学
  EASTERN_PHILOSOPHY = 'eastern_philosophy', // 东方哲学
  ANCIENT_PHILOSOPHY = 'ancient_philosophy', // 古代哲学
  MODERN_PHILOSOPHY = 'modern_philosophy', // 现代哲学
  CONTEMPORARY_PHILOSOPHY = 'contemporary_philosophy' // 当代哲学
}

// 概念元数据
export interface ConceptMetadata {
  philosophers: Philosopher[]; // 相关哲学家
  works: PhilosophicalWork[]; // 相关著作
  historicalPeriod: string; // 历史时期
  schools: string[]; // 哲学流派
  keywords: string[]; // 关键词
  difficulty: number; // 难度等级 1-5
  popularity: number; // 受关注程度 0-1
}

// 哲学家信息
export interface Philosopher {
  id: string;
  name: string;
  nameEn?: string; // 英文名
  lifespan: string; // 生卒年份
  nationality: string;
  school: string[]; // 所属流派
  mainContributions: string[];
  portrait?: string; // 头像URL
}

// 哲学著作
export interface PhilosophicalWork {
  id: string;
  title: string;
  titleEn?: string;
  author: string;
  year: number;
  description: string;
  significance: string; // 重要性说明
  url?: string; // 相关链接
}

// 关系边类型
export interface ConceptEdge {
  id: string;
  source: string | ConceptNode; // 源节点ID或节点对象
  target: string | ConceptNode; // 目标节点ID或节点对象
  type: RelationType;
  strength: number; // 关系强度 0-1
  description?: string; // 关系描述
  bidirectional: boolean; // 是否双向关系
}

// 关系类型
export enum RelationType {
  CONTAINS = 'contains', // 包含关系
  INFLUENCES = 'influences', // 影响关系
  OPPOSES = 'opposes', // 对立关系
  DEVELOPS = 'develops', // 发展关系
  APPLIES = 'applies', // 应用关系
  RELATES = 'relates', // 相关关系
  DERIVES = 'derives', // 派生关系
  CRITIQUES = 'critiques', // 批判关系
  SYNTHESIZES = 'synthesizes', // 综合关系
  EXEMPLIFIES = 'exemplifies' // 例证关系
}

// 完整的知识图谱
export interface KnowledgeGraph {
  id: string;
  topic: string; // 主题
  description: string;
  nodes: ConceptNode[];
  edges: ConceptEdge[];
  metadata: GraphMetadata;
  createdAt: Date;
  updatedAt: Date;
}

// 图谱元数据
export interface GraphMetadata {
  totalConcepts: number;
  maxDepth: number; // 最大层级深度
  categories: PhilosophyCategory[];
  complexity: number; // 复杂度评分 1-10
  completeness: number; // 完整度 0-1
  sources: string[]; // 数据来源
}

// AI分析请求
export interface AnalysisRequest {
  topic: string;
  depth: number; // 分析深度 1-5
  focus?: PhilosophyCategory[]; // 关注的哲学分类
  language: 'zh' | 'en'; // 语言偏好
  includeHistorical: boolean; // 是否包含历史背景
  includeContemporary: boolean; // 是否包含当代观点
}

// AI分析响应
export interface AnalysisResponse {
  success: boolean;
  graph: KnowledgeGraph;
  insights: string[]; // AI生成的洞察
  suggestions: string[]; // 进一步探索建议
  confidence: number; // 分析置信度 0-1
  processingTime: number; // 处理时间（毫秒）
}

// 可视化配置
export interface VisualizationConfig {
  width: number;
  height: number;
  nodeSize: {
    min: number;
    max: number;
  };
  colors: {
    [key in PhilosophyCategory]: string;
  };
  layout: 'force' | 'hierarchical' | 'circular';
  showLabels: boolean;
  showEdgeLabels: boolean;
  animationDuration: number;
  zoomExtent: [number, number];
}

// 搜索和过滤
export interface SearchFilters {
  categories?: PhilosophyCategory[];
  difficulty?: number[];
  timeperiod?: string[];
  philosophers?: string[];
  keywords?: string[];
}

export interface SearchResult {
  nodes: ConceptNode[];
  edges: ConceptEdge[];
  totalCount: number;
  query: string;
  filters: SearchFilters;
}
