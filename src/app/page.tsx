'use client';

import React, { useState, useCallback } from 'react';
import { KnowledgeGraph, ConceptNode, AnalysisRequest } from '@/types/knowledge-graph';
import { sampleKnowledgeGraph } from '@/data/sample-data';
import KnowledgeGraphVisualization from '@/components/KnowledgeGraphVisualization';
import ConceptDetailPanel from '@/components/ConceptDetailPanel';
import SearchInterface from '@/components/SearchInterface';
import LoadingSpinner from '@/components/LoadingSpinner';

export default function Home() {
  const [currentGraph, setCurrentGraph] = useState<KnowledgeGraph | null>(null);
  const [selectedNode, setSelectedNode] = useState<ConceptNode | null>(null);
  const [isDetailPanelOpen, setIsDetailPanelOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSearch = useCallback(async (request: AnalysisRequest) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error('分析请求失败');
      }

      const result = await response.json();

      if (result.success) {
        setCurrentGraph(result.graph);
        setSelectedNode(null);
        setIsDetailPanelOpen(false);
      } else {
        setError(result.error || '分析失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '网络错误');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleNodeClick = useCallback((node: ConceptNode) => {
    setSelectedNode(node);
    setIsDetailPanelOpen(true);
  }, []);

  const handleCloseDetailPanel = useCallback(() => {
    setIsDetailPanelOpen(false);
    setSelectedNode(null);
  }, []);

  const handleLoadSample = useCallback(() => {
    setCurrentGraph(sampleKnowledgeGraph);
    setSelectedNode(null);
    setIsDetailPanelOpen(false);
    setError(null);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">哲</span>
              </div>
              <h1 className="text-xl font-bold text-gray-900">
                哲学知识图谱
              </h1>
            </div>
            <div className="text-sm text-gray-500">
              探索哲学概念的内在联系
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8 py-4 sm:py-8">
        {/* 搜索界面 */}
        <div className="mb-4 sm:mb-8">
          <SearchInterface
            onSearch={handleSearch}
            isLoading={isLoading}
          />
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">!</span>
              </div>
              <span className="text-red-800 font-medium">错误</span>
            </div>
            <p className="text-red-700 mt-1">{error}</p>
          </div>
        )}

        {/* 加载状态 */}
        {isLoading && (
          <div className="flex items-center justify-center py-12">
            <LoadingSpinner />
          </div>
        )}

        {/* 知识图谱可视化 */}
        {!isLoading && currentGraph && (
          <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
            <div className="p-3 sm:p-4 border-b bg-gray-50">
              <h2 className="text-base sm:text-lg font-semibold text-gray-900">
                {currentGraph.topic} - 知识图谱
              </h2>
              <p className="text-xs sm:text-sm text-gray-600 mt-1">
                {currentGraph.description}
              </p>
              <div className="flex items-center gap-2 sm:gap-4 mt-2 text-xs text-gray-500 flex-wrap">
                <span>概念: {currentGraph.nodes.length}</span>
                <span>关系: {currentGraph.edges.length}</span>
                <span>复杂度: {currentGraph.metadata.complexity}/10</span>
              </div>
            </div>

            <div className="relative">
              <KnowledgeGraphVisualization
                graph={currentGraph}
                onNodeClick={handleNodeClick}
                config={{
                  width: typeof window !== 'undefined' ? Math.min(1000, window.innerWidth - 40) : 1000,
                  height: typeof window !== 'undefined' && window.innerWidth < 768 ? 400 : 600
                }}
                className="w-full"
              />
            </div>
          </div>
        )}

        {/* 使用说明 */}
        {!currentGraph && !isLoading && (
          <div className="text-center py-12">
            <div className="max-w-md mx-auto">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-blue-600 text-2xl">🧠</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                开始探索哲学世界
              </h3>
              <p className="text-gray-600 mb-4">
                输入您感兴趣的哲学主题，AI将为您生成相关的概念知识图谱
              </p>
              <div className="text-sm text-gray-500 mb-6">
                <p>支持的主题包括：</p>
                <p>存在主义、道德哲学、自由意志、心灵哲学等</p>
              </div>
              <button
                onClick={handleLoadSample}
                className="px-6 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors"
              >
                📖 查看示例图谱
              </button>
            </div>
          </div>
        )}
      </main>

      {/* 概念详情面板 */}
      <ConceptDetailPanel
        node={selectedNode}
        isOpen={isDetailPanelOpen}
        onClose={handleCloseDetailPanel}
      />

      {/* 遮罩层 */}
      {isDetailPanelOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-25 z-40"
          onClick={handleCloseDetailPanel}
        />
      )}
    </div>
  );
}
