import { 
  KnowledgeGraph, 
  ConceptNode, 
  ConceptEdge, 
  PhilosophyCategory, 
  RelationType,
  Philosopher,
  PhilosophicalWork 
} from '@/types/knowledge-graph';

// 示例哲学家数据
export const samplePhilosophers: Philosopher[] = [
  {
    id: 'sartre',
    name: '让-保罗·萨特',
    nameEn: '<PERSON><PERSON><PERSON>',
    lifespan: '1905-1980',
    nationality: '法国',
    school: ['存在主义', '现象学'],
    mainContributions: ['存在先于本质', '自由选择理论', '坏信仰概念']
  },
  {
    id: 'camus',
    name: '阿尔贝·加缪',
    nameEn: '<PERSON>',
    lifespan: '1913-1960',
    nationality: '法国',
    school: ['存在主义', '荒诞主义'],
    mainContributions: ['荒诞哲学', '反抗理论', '西西弗神话']
  },
  {
    id: 'kierkegaard',
    name: '索伦·克尔凯郭尔',
    nameEn: '<PERSON><PERSON><PERSON>',
    lifespan: '1813-1855',
    nationality: '丹麦',
    school: ['存在主义先驱', '基督教存在主义'],
    mainContributions: ['焦虑概念', '信仰的跳跃', '个体性理论']
  }
];

// 示例哲学著作数据
export const sampleWorks: PhilosophicalWork[] = [
  {
    id: 'being-and-nothingness',
    title: '存在与虚无',
    titleEn: 'Being and Nothingness',
    author: '让-保罗·萨特',
    year: 1943,
    description: '萨特的主要哲学著作，阐述了存在主义的核心理念',
    significance: '确立了存在主义哲学的理论基础'
  },
  {
    id: 'the-stranger',
    title: '局外人',
    titleEn: 'The Stranger',
    author: '阿尔贝·加缪',
    year: 1942,
    description: '通过小说形式探讨荒诞主义哲学',
    significance: '文学与哲学完美结合的典范'
  },
  {
    id: 'fear-and-trembling',
    title: '恐惧与颤栗',
    titleEn: 'Fear and Trembling',
    author: '索伦·克尔凯郭尔',
    year: 1843,
    description: '探讨信仰、焦虑与个体选择的关系',
    significance: '存在主义哲学的重要先驱作品'
  }
];

// 示例概念节点数据
export const sampleNodes: ConceptNode[] = [
  {
    id: 'existentialism',
    name: '存在主义',
    description: '强调个体存在、自由选择和个人责任的哲学流派',
    category: PhilosophyCategory.EXISTENTIALISM,
    importance: 1.0,
    level: 0,
    // 新增详细解析字段
    type: 'core',
    conceptType: '承重墙',
    essence: '存在主义的核心在于强调人的存在先于本质，每个人都必须通过自由选择来定义自己的本质和意义。',
    significance: '这是现代哲学的重要转折点，它将哲学的焦点从抽象的本质转向具体的个体存在，强调了人的主观能动性和责任。',
    practice: '在日常生活中，可以通过反思自己的选择和行为，承担起塑造自我的责任，拒绝被外在标签或社会期待所定义。',
    pitfalls: [
      '误以为存在主义就是虚无主义或相对主义',
      '忽视存在主义强调的个人责任，只关注自由而不承担后果'
    ],
    secretTips: [
      {
        name: '焦虑的积极意义',
        scenario: '面对重大人生选择时',
        advantage: '焦虑实际上是自由的标志，表明你意识到了选择的重要性',
        operation: '将焦虑视为成长的机会，通过深入思考来做出真正属于自己的选择'
      }
    ],
    metadata: {
      philosophers: [samplePhilosophers[0], samplePhilosophers[1]],
      works: [sampleWorks[0], sampleWorks[1]],
      historicalPeriod: '20世纪',
      schools: ['存在主义'],
      keywords: ['存在', '自由', '选择', '责任', '焦虑'],
      difficulty: 4,
      popularity: 0.8
    }
  },
  {
    id: 'existence-precedes-essence',
    name: '存在先于本质',
    description: '萨特提出的核心概念，认为人的存在先于其本质',
    category: PhilosophyCategory.EXISTENTIALISM,
    importance: 0.9,
    level: 1,
    // 新增详细解析字段
    type: 'core',
    conceptType: '承重墙',
    essence: '与传统哲学认为事物先有本质再有存在不同，萨特认为人类是先存在，然后通过自己的行动和选择来创造自己的本质。',
    significance: '这个概念彻底颠覆了传统的人性观，强调人没有预定的本质或目的，必须为自己的存在负责。',
    practice: '每天问自己：我今天的选择如何定义了我是谁？通过有意识的选择来塑造自己想成为的人。',
    pitfalls: [
      '认为这意味着人可以随意改变而没有连续性',
      '忽视社会环境和历史条件对个人选择的影响'
    ],
    metadata: {
      philosophers: [samplePhilosophers[0]],
      works: [sampleWorks[0]],
      historicalPeriod: '20世纪',
      schools: ['存在主义'],
      keywords: ['存在', '本质', '自由', '创造'],
      difficulty: 5,
      popularity: 0.7
    }
  },
  {
    id: 'absurdism',
    name: '荒诞主义',
    description: '认为人类寻求意义的努力与世界无意义本质之间存在根本冲突',
    category: PhilosophyCategory.EXISTENTIALISM,
    importance: 0.8,
    level: 1,
    // 新增详细解析字段
    type: 'supporting',
    conceptType: '装饰品',
    essence: '荒诞主义指出了人类理性寻求意义的冲动与世界本身无意义之间的永恒张力。',
    significance: '它帮助我们理解现代人的困境，并提供了一种既不逃避也不绝望的生活态度。',
    practice: '接受生活的荒诞性，同时继续热情地生活，如西西弗推石头般坚持不懈。',
    secretTips: [
      {
        name: '荒诞的解放力量',
        scenario: '感到生活无意义时',
        advantage: '认识到荒诞可以解放我们，不再被虚假的绝对意义所束缚',
        operation: '专注于当下的体验和创造，而不是寻找终极答案'
      }
    ],
    metadata: {
      philosophers: [samplePhilosophers[1]],
      works: [sampleWorks[1]],
      historicalPeriod: '20世纪',
      schools: ['荒诞主义', '存在主义'],
      keywords: ['荒诞', '意义', '反抗', '西西弗'],
      difficulty: 4,
      popularity: 0.6
    }
  },
  {
    id: 'anxiety',
    name: '焦虑',
    description: '面对自由选择时产生的根本性情感体验',
    category: PhilosophyCategory.EXISTENTIALISM,
    importance: 0.7,
    level: 2,
    metadata: {
      philosophers: [samplePhilosophers[0], samplePhilosophers[2]],
      works: [sampleWorks[0], sampleWorks[2]],
      historicalPeriod: '19-20世纪',
      schools: ['存在主义'],
      keywords: ['焦虑', '自由', '选择', '恐惧'],
      difficulty: 3,
      popularity: 0.5
    }
  },
  {
    id: 'bad-faith',
    name: '坏信仰',
    description: '个体为逃避自由和责任而采取的自欺行为',
    category: PhilosophyCategory.EXISTENTIALISM,
    importance: 0.6,
    level: 2,
    metadata: {
      philosophers: [samplePhilosophers[0]],
      works: [sampleWorks[0]],
      historicalPeriod: '20世纪',
      schools: ['存在主义'],
      keywords: ['坏信仰', '自欺', '逃避', '责任'],
      difficulty: 4,
      popularity: 0.4
    }
  },
  {
    id: 'freedom',
    name: '自由',
    description: '存在主义核心概念，强调人类的根本自由和选择能力',
    category: PhilosophyCategory.EXISTENTIALISM,
    importance: 0.9,
    level: 1,
    metadata: {
      philosophers: [samplePhilosophers[0], samplePhilosophers[1]],
      works: [sampleWorks[0]],
      historicalPeriod: '20世纪',
      schools: ['存在主义'],
      keywords: ['自由', '选择', '责任', '决定'],
      difficulty: 3,
      popularity: 0.9
    }
  },
  {
    id: 'authenticity',
    name: '真实性',
    description: '按照自己的本真存在方式生活，不受外在期望束缚',
    category: PhilosophyCategory.EXISTENTIALISM,
    importance: 0.7,
    level: 2,
    metadata: {
      philosophers: [samplePhilosophers[0], samplePhilosophers[2]],
      works: [sampleWorks[0], sampleWorks[2]],
      historicalPeriod: '19-20世纪',
      schools: ['存在主义'],
      keywords: ['真实性', '本真', '自我', '社会'],
      difficulty: 4,
      popularity: 0.6
    }
  }
];

// 示例关系边数据
export const sampleEdges: ConceptEdge[] = [
  {
    id: 'edge-1',
    source: 'existentialism',
    target: 'existence-precedes-essence',
    type: RelationType.CONTAINS,
    strength: 0.9,
    description: '存在主义包含"存在先于本质"这一核心概念',
    bidirectional: false
  },
  {
    id: 'edge-2',
    source: 'existentialism',
    target: 'absurdism',
    type: RelationType.RELATES,
    strength: 0.7,
    description: '荒诞主义与存在主义密切相关',
    bidirectional: true
  },
  {
    id: 'edge-3',
    source: 'existentialism',
    target: 'freedom',
    type: RelationType.CONTAINS,
    strength: 0.9,
    description: '自由是存在主义的核心概念',
    bidirectional: false
  },
  {
    id: 'edge-4',
    source: 'freedom',
    target: 'anxiety',
    type: RelationType.INFLUENCES,
    strength: 0.8,
    description: '自由选择导致焦虑的产生',
    bidirectional: false
  },
  {
    id: 'edge-5',
    source: 'freedom',
    target: 'bad-faith',
    type: RelationType.OPPOSES,
    strength: 0.7,
    description: '坏信仰是对自由的逃避',
    bidirectional: false
  },
  {
    id: 'edge-6',
    source: 'existence-precedes-essence',
    target: 'authenticity',
    type: RelationType.INFLUENCES,
    strength: 0.6,
    description: '存在先于本质的理念影响真实性概念',
    bidirectional: false
  },
  {
    id: 'edge-7',
    source: 'anxiety',
    target: 'authenticity',
    type: RelationType.RELATES,
    strength: 0.5,
    description: '焦虑与寻求真实性相关',
    bidirectional: true
  }
];

// 示例知识图谱
export const sampleKnowledgeGraph: KnowledgeGraph = {
  id: 'existentialism-graph',
  topic: '存在主义',
  description: '探索存在主义哲学的核心概念和相互关系',
  nodes: sampleNodes,
  edges: sampleEdges,
  metadata: {
    totalConcepts: sampleNodes.length,
    maxDepth: 2,
    categories: [PhilosophyCategory.EXISTENTIALISM],
    complexity: 7,
    completeness: 0.8,
    sources: ['Stanford Encyclopedia of Philosophy', '哲学百科全书']
  },
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date()
};

// 其他示例主题的简化数据
export const sampleTopics = [
  '存在主义',
  '道德哲学',
  '自由意志',
  '心灵哲学',
  '认识论',
  '形而上学',
  '政治哲学',
  '美学',
  '逻辑学',
  '宗教哲学'
];

export const sampleCategories = Object.values(PhilosophyCategory);
