import { 
  AnalysisRequest, 
  KnowledgeGraph,
  ConceptNode,
  ConceptEdge,
  PhilosophyCategory,
  RelationType,
  Philosopher,
  PhilosophicalWork 
} from '@/types/knowledge-graph';

// AI分析结果接口
interface AIAnalysisResult {
  graph: KnowledgeGraph;
  insights: string[];
  suggestions: string[];
  confidence: number;
  // 新增详细解析字段
  coreInsight?: string; // 核心洞察
  conceptMap?: string; // 概念地图
  actionGuide?: {
    beginner: string[];
    advanced: string[];
  };
  mentorMessage?: string; // 导师寄语
}

// 哲学概念模板库
const philosophyTemplates = {
  '存在主义': {
    category: PhilosophyCategory.EXISTENTIALISM,
    coreConcepts: [
      { name: '存在先于本质', importance: 0.9, level: 1 },
      { name: '自由选择', importance: 0.8, level: 1 },
      { name: '焦虑', importance: 0.7, level: 2 },
      { name: '真实性', importance: 0.7, level: 2 },
      { name: '坏信仰', importance: 0.6, level: 2 },
      { name: '荒诞', importance: 0.8, level: 1 },
      { name: '责任', importance: 0.7, level: 2 }
    ],
    philosophers: [
      { name: '让-保罗·萨特', contribution: '存在先于本质理论' },
      { name: '阿尔贝·加缪', contribution: '荒诞主义哲学' },
      { name: '索伦·克尔凯郭尔', contribution: '焦虑概念的先驱' },
      { name: '马丁·海德格尔', contribution: '此在概念' }
    ]
  },
  '道德哲学': {
    category: PhilosophyCategory.ETHICS,
    coreConcepts: [
      { name: '功利主义', importance: 0.9, level: 1 },
      { name: '义务论', importance: 0.9, level: 1 },
      { name: '德性伦理学', importance: 0.8, level: 1 },
      { name: '道德相对主义', importance: 0.7, level: 2 },
      { name: '道德实在论', importance: 0.7, level: 2 },
      { name: '最大幸福原则', importance: 0.6, level: 2 },
      { name: '绝对命令', importance: 0.8, level: 2 }
    ],
    philosophers: [
      { name: '伊曼努尔·康德', contribution: '绝对命令理论' },
      { name: '约翰·斯图尔特·密尔', contribution: '功利主义发展' },
      { name: '亚里士多德', contribution: '德性伦理学' },
      { name: '杰里米·边沁', contribution: '功利主义创立' }
    ]
  },
  '自由意志': {
    category: PhilosophyCategory.PHILOSOPHY_OF_MIND,
    coreConcepts: [
      { name: '决定论', importance: 0.9, level: 1 },
      { name: '非决定论', importance: 0.8, level: 1 },
      { name: '相容论', importance: 0.8, level: 1 },
      { name: '不相容论', importance: 0.7, level: 2 },
      { name: '因果决定论', importance: 0.7, level: 2 },
      { name: '道德责任', importance: 0.8, level: 2 },
      { name: '选择的自由', importance: 0.6, level: 2 }
    ],
    philosophers: [
      { name: '大卫·休谟', contribution: '相容论观点' },
      { name: '伊曼努尔·康德', contribution: '超验自由概念' },
      { name: '威廉·詹姆斯', contribution: '非决定论立场' },
      { name: '丹尼尔·丹尼特', contribution: '现代相容论' }
    ]
  }
};

/**
 * 调用OpenRouter API生成哲学概念分析
 */
async function callOpenRouterAPI(prompt: string): Promise<string> {
  const apiKey = process.env.OPENROUTER_API_KEY;
  const model = process.env.OPENROUTER_MODEL || 'google/gemini-2.5-pro';
  const baseUrl = process.env.OPENROUTER_BASE_URL || 'https://openrouter.ai/api/v1';

  if (!apiKey) {
    throw new Error('OpenRouter API key not configured');
  }

  try {
    const response = await fetch(`${baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'http://localhost:3000',
        'X-Title': 'Philosophy Knowledge Graph'
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: 'system',
            content: '你是一位专业的哲学教授和知识图谱专家。请根据用户提供的哲学主题，分析其核心概念、子概念和相互关系，并以结构化的JSON格式返回。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 4000
      })
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || '';
  } catch (error) {
    console.error('OpenRouter API call failed:', error);
    throw error;
  }
}

/**
 * 生成哲学知识图谱
 */
export async function generatePhilosophyGraph(request: AnalysisRequest): Promise<AIAnalysisResult> {
  const { topic, depth, focus, language = 'zh', includeHistorical = true, includeContemporary = true } = request;

  try {
    // 构建AI提示词
    const prompt = buildAnalysisPrompt(topic, depth, focus, includeHistorical, includeContemporary, language);
    console.log('🤖 Calling OpenRouter API for topic:', topic);

    // 调用OpenRouter API
    const aiResponse = await callOpenRouterAPI(prompt);
    console.log('📥 AI Response received, length:', aiResponse.length);
    console.log('📄 AI Response preview:', aiResponse.substring(0, 200) + '...');

    // 尝试简单的AI分析
    const simpleResult = createSimpleAIResult(aiResponse, topic, depth);
    if (simpleResult) {
      console.log('✅ Simple AI analysis successful');
      return simpleResult;
    }

    // 解析AI响应
    const parsedResult = parseAIResponse(aiResponse, topic);

    if (parsedResult) {
      console.log('✅ AI analysis successful, nodes:', parsedResult.graph.nodes.length);
      return parsedResult;
    }
  } catch (error) {
    console.error('❌ AI analysis failed, falling back to template:', error);
  }

  // 如果AI分析失败，回退到模板
  const template = philosophyTemplates[topic as keyof typeof philosophyTemplates];

  if (!template) {
    // 如果没有预定义模板，生成通用结构
    return generateGenericPhilosophyGraph(request);
  }

  // 基于模板生成概念节点
  const nodes: ConceptNode[] = [];
  
  // 添加主题根节点
  const rootNode: ConceptNode = {
    id: generateId(topic),
    name: topic,
    description: `${topic}的核心概念和理论体系`,
    category: template.category,
    importance: 1.0,
    level: 0,
    metadata: {
      philosophers: template.philosophers.map(p => ({
        id: generateId(p.name),
        name: p.name,
        nameEn: '',
        lifespan: '',
        nationality: '',
        school: [],
        mainContributions: [p.contribution]
      })),
      works: [],
      historicalPeriod: includeHistorical ? '古代至现代' : '现代',
      schools: [topic],
      keywords: [topic],
      difficulty: Math.min(depth + 1, 5),
      popularity: 0.8
    }
  };
  nodes.push(rootNode);

  // 添加核心概念节点
  template.coreConcepts.slice(0, depth * 2).forEach(concept => {
    const node: ConceptNode = {
      id: generateId(concept.name),
      name: concept.name,
      description: generateConceptDescription(concept.name, topic),
      category: template.category,
      importance: concept.importance,
      level: concept.level,
      metadata: {
        philosophers: template.philosophers.slice(0, 2).map(p => ({
          id: generateId(p.name),
          name: p.name,
          nameEn: '',
          lifespan: '',
          nationality: '',
          school: [],
          mainContributions: [p.contribution]
        })),
        works: [],
        historicalPeriod: includeHistorical ? '19-20世纪' : '现代',
        schools: [topic],
        keywords: [concept.name, topic],
        difficulty: Math.min(concept.level + 2, 5),
        popularity: concept.importance * 0.8
      }
    };
    nodes.push(node);
  });

  // 生成关系边
  const edges: ConceptEdge[] = [];
  
  // 根节点与一级概念的关系
  nodes.filter(n => n.level === 1).forEach(node => {
    edges.push({
      id: `edge-${rootNode.id}-${node.id}`,
      source: rootNode.id,
      target: node.id,
      type: RelationType.CONTAINS,
      strength: 0.9,
      description: `${topic}包含${node.name}概念`,
      bidirectional: false
    });
  });

  // 概念间的关系
  const level1Nodes = nodes.filter(n => n.level === 1);
  const level2Nodes = nodes.filter(n => n.level === 2);
  
  level2Nodes.forEach(level2Node => {
    const relatedLevel1 = level1Nodes[Math.floor(Math.random() * level1Nodes.length)];
    edges.push({
      id: `edge-${relatedLevel1.id}-${level2Node.id}`,
      source: relatedLevel1.id,
      target: level2Node.id,
      type: getRandomRelationType(),
      strength: 0.6 + Math.random() * 0.3,
      description: `${relatedLevel1.name}与${level2Node.name}相关`,
      bidirectional: Math.random() > 0.5
    });
  });

  // 构建知识图谱
  const graph: KnowledgeGraph = {
    id: generateId(`${topic}-graph`),
    topic,
    description: `关于${topic}的知识图谱分析`,
    nodes,
    edges,
    metadata: {
      totalConcepts: nodes.length,
      maxDepth: Math.max(...nodes.map(n => n.level)),
      categories: [template.category],
      complexity: Math.min(depth * 2, 10),
      completeness: Math.min(0.6 + depth * 0.1, 1.0),
      sources: ['AI生成', '哲学百科全书']
    },
    createdAt: new Date(),
    updatedAt: new Date()
  };

  // 生成洞察和建议
  const insights = generateInsights(topic, nodes.length, template.category);
  const suggestions = generateSuggestions(topic, depth);

  return {
    graph,
    insights,
    suggestions,
    confidence: 0.75 + Math.random() * 0.2
  };
}

/**
 * 生成通用哲学图谱（当没有预定义模板时）
 */
function generateGenericPhilosophyGraph(request: AnalysisRequest): AIAnalysisResult {
  const { topic, depth } = request;
  
  const nodes: ConceptNode[] = [{
    id: generateId(topic),
    name: topic,
    description: `${topic}的相关概念和理论`,
    category: PhilosophyCategory.CONTEMPORARY_PHILOSOPHY,
    importance: 1.0,
    level: 0,
    metadata: {
      philosophers: [],
      works: [],
      historicalPeriod: '现代',
      schools: [topic],
      keywords: [topic],
      difficulty: 3,
      popularity: 0.5
    }
  }];

  const edges: ConceptEdge[] = [];

  const graph: KnowledgeGraph = {
    id: generateId(`${topic}-graph`),
    topic,
    description: `关于${topic}的基础知识图谱`,
    nodes,
    edges,
    metadata: {
      totalConcepts: 1,
      maxDepth: 0,
      categories: [PhilosophyCategory.CONTEMPORARY_PHILOSOPHY],
      complexity: 3,
      completeness: 0.3,
      sources: ['AI生成']
    },
    createdAt: new Date(),
    updatedAt: new Date()
  };

  return {
    graph,
    insights: [`${topic}是一个值得深入探索的哲学主题`],
    suggestions: [`建议进一步研究${topic}的历史发展`, `探索${topic}与其他哲学概念的关系`],
    confidence: 0.5
  };
}

/**
 * 构建AI分析提示词
 */
function buildAnalysisPrompt(
  topic: string,
  depth: number,
  focus?: PhilosophyCategory[],
  includeHistorical?: boolean,
  includeContemporary?: boolean,
  language?: string
): string {
  const focusText = focus && focus.length > 0 ? `，特别关注${focus.join('、')}领域` : '';
  const historicalText = includeHistorical ? '包含历史发展脉络' : '';
  const contemporaryText = includeContemporary ? '包含当代观点' : '';

  return `=== 🎭 角色设定 ===
你是一位在相关领域深耕多年的资深专家，同时也是一位富有同理心的导师。
你既拥有俯瞰全局的战略视野，又保持着对初学者困境的深刻理解。
你的使命是将复杂的知识体系转化为清晰的认知地图。

=== 🧭 工作流程 ===

当用户输入一个知识概念时，请按以下步骤执行：

用户输入的概念是："${topic}"

### 第一步：概念解构 🔍
1. 识别用户输入的核心概念
2. 将其拆解为${Math.max(3, depth + 2)}-${Math.min(7, depth + 5)}个关键子概念（数量依复杂度而定）
3. 构建概念间的逻辑关系图谱

### 第二步：智慧提炼 💎
对每个子概念，遵循以下原则进行解释：

**承重墙概念**（核心必懂）
- 这是什么？（本质定义）
- 为什么重要？（价值所在）
- 如何应用？（实践路径）
- 常见误区？（避坑指南）

**装饰品概念**（锦上添花）
- 简明扼要地说明其作用
- 点出与核心概念的关联
- 给出"是否深入"的建议

**暗门概念**（高手秘籍）
- 揭示不为人知的技巧
- 分享实战中的妙用
- 标注使用的时机和场景

请严格按照以下JSON格式返回分析结果：

{
  "coreInsight": "用一句话点出该概念的核心价值",
  "conceptMap": "用简洁的方式展示概念拆解结构",
  "concepts": [
    {
      "name": "概念名称",
      "description": "深入浅出的解释，体现深度",
      "importance": 0.9,
      "level": 0,
      "category": "core|supporting|advanced",
      "type": "承重墙|装饰品|暗门",
      "essence": "本质理解：深入浅出的解释",
      "significance": "为何关键：说明其不可或缺的原因",
      "practice": "实战指南：具体可操作的建议",
      "pitfalls": ["常见陷阱1", "常见陷阱2"],
      "philosophers": [{"name": "相关哲学家", "contribution": "主要贡献"}],
      "secretTips": [
        {
          "name": "技巧名称",
          "scenario": "什么时候用",
          "advantage": "为什么好用",
          "operation": "怎么实施"
        }
      ]
    }
  ],
  "relationships": [
    {
      "source": "概念1",
      "target": "概念2",
      "type": "contains|influences|opposes|relates|derives",
      "strength": 0.8,
      "description": "关系的深层意义"
    }
  ],
  "actionGuide": {
    "beginner": ["具体建议1", "具体建议2", "具体建议3"],
    "advanced": ["进阶路径建议1", "进阶路径建议2"]
  },
  "mentorMessage": "用1-2段话，分享你对这个概念的独特见解，给学习者以启发和鼓励"
}

要求：
- 拆解要精准：抓住真正的核心，而非表面分类
- 解释要透彻：既有深度又不失亲和力
- 结构要清晰：让人一眼看懂脉络
- 价值要实在：读完就能用得上
- 确保JSON格式完全正确
${focusText}${historicalText ? '，' + historicalText : ''}${contemporaryText ? '，' + contemporaryText : ''}`;
}

/**
 * 创建简单的AI分析结果（当JSON解析失败时）
 */
function createSimpleAIResult(aiResponse: string, topic: string, depth: number): AIAnalysisResult | null {
  try {
    // 更智能地从AI响应中提取哲学概念
    const concepts: string[] = [];

    // 首先尝试从JSON结构中提取
    try {
      const jsonMatch = aiResponse.match(/```json\s*([\s\S]*?)\s*```/) || aiResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const jsonStr = jsonMatch[1] || jsonMatch[0];

        // 尝试解析JSON
        try {
          const parsed = JSON.parse(jsonStr);
          if (parsed.concepts && Array.isArray(parsed.concepts)) {
            parsed.concepts.forEach((concept: any) => {
              if (concept.name && isValidPhilosophyConcept(concept.name)) {
                concepts.push(concept.name);
              }
            });
          }
        } catch (parseError) {
          // 如果JSON解析失败，使用正则提取
          const nameMatches = jsonStr.match(/"name"\s*:\s*"([^"]+)"/g);
          if (nameMatches) {
            nameMatches.forEach(match => {
              const conceptMatch = match.match(/"name"\s*:\s*"([^"]+)"/);
              if (conceptMatch && conceptMatch[1]) {
                const concept = conceptMatch[1].trim();
                if (isValidPhilosophyConcept(concept)) {
                  concepts.push(concept);
                }
              }
            });
          }
        }
      }
    } catch (error) {
      console.log('JSON extraction failed, trying text patterns');
    }

    // 如果JSON提取失败，使用文本模式
    if (concepts.length < 2) {
      const lines = aiResponse.split('\n');

      for (const line of lines) {
        const trimmed = line.trim();

        // 匹配哲学概念的模式
        const patterns = [
          /^[\d\-\*\+]\s*([^：:]+)/, // 列表项
          /^([^：:，,]+)[：:]/, // 冒号前的内容
          /^"([^"]+)"(?:\s*[：:]|$)/, // 引号包围的概念
          /^([^，,]+)，/, // 逗号前的内容
        ];

        for (const pattern of patterns) {
          const match = trimmed.match(pattern);
          if (match && match[1]) {
            const concept = match[1].trim();
            if (isValidPhilosophyConcept(concept)) {
              concepts.push(concept);
            }
          }
        }
      }
    }

    // 如果仍然没有找到足够的概念，使用备用概念
    if (concepts.length < 3) {
      const fallbackConcepts = getFallbackConcepts(topic, depth);
      concepts.push(...fallbackConcepts);
    }

    // 限制概念数量并去重
    const uniqueConcepts = [...new Set(concepts)].slice(0, depth * 3);

    if (uniqueConcepts.length === 0) {
      return null;
    }

    // 创建节点
    const nodes: ConceptNode[] = uniqueConcepts.map((concept, index) => ({
      id: generateId(concept),
      name: concept,
      description: generateConceptDescription(concept, topic),
      category: getPhilosophyCategory(topic),
      importance: Math.max(0.4, 1 - index * 0.08),
      level: Math.min(Math.floor(index / 2), depth),
      metadata: {
        philosophers: getRelatedPhilosophers(concept, topic),
        works: [],
        historicalPeriod: getHistoricalPeriod(topic),
        schools: [topic],
        keywords: [concept, topic],
        difficulty: Math.min(3 + Math.floor(index / 2), 5),
        popularity: Math.max(0.4, 1 - index * 0.08)
      }
    }));

    // 创建简单的关系
    const edges: ConceptEdge[] = [];
    for (let i = 1; i < nodes.length; i++) {
      edges.push({
        id: generateId(`${nodes[0].id}-${nodes[i].id}`),
        source: nodes[0].id,
        target: nodes[i].id,
        type: RelationType.RELATES,
        strength: 0.6,
        description: `${nodes[0].name}与${nodes[i].name}相关`,
        bidirectional: false
      });
    }

    const graph: KnowledgeGraph = {
      id: generateId(`${topic}-simple-ai-graph`),
      topic,
      description: `AI分析的${topic}概念图谱`,
      nodes,
      edges,
      metadata: {
        totalConcepts: nodes.length,
        maxDepth: Math.max(...nodes.map(n => n.level)),
        categories: [PhilosophyCategory.CONTEMPORARY_PHILOSOPHY],
        complexity: Math.min(nodes.length, 8),
        completeness: 0.7,
        sources: ['AI分析', 'OpenRouter API']
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return {
      graph,
      insights: [
        `AI识别了${nodes.length}个与${topic}相关的核心概念`,
        `这些概念展现了${topic}的多维度特征`,
        `建议深入研究每个概念的具体内涵`
      ],
      suggestions: [
        `进一步探索${topic}的历史发展`,
        `阅读相关的经典哲学著作`,
        `思考这些概念在现代社会中的应用`
      ],
      confidence: 0.75
    };

  } catch (error) {
    console.error('Simple AI analysis failed:', error);
    return null;
  }
}

/**
 * 解析AI响应
 */
function parseAIResponse(aiResponse: string, topic: string): AIAnalysisResult | null {
  try {
    // 清理响应文本
    let cleanResponse = aiResponse.trim();

    // 尝试多种方式提取JSON
    let jsonStr = '';

    // 方法1: 寻找完整的JSON对象
    const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      jsonStr = jsonMatch[0];
    } else {
      // 方法2: 寻找```json代码块
      const codeBlockMatch = cleanResponse.match(/```json\s*([\s\S]*?)\s*```/);
      if (codeBlockMatch) {
        jsonStr = codeBlockMatch[1];
      } else {
        // 方法3: 寻找```代码块
        const generalCodeMatch = cleanResponse.match(/```\s*([\s\S]*?)\s*```/);
        if (generalCodeMatch) {
          jsonStr = generalCodeMatch[1];
        } else {
          throw new Error('No JSON found in AI response');
        }
      }
    }

    // 尝试修复常见的JSON格式问题
    jsonStr = jsonStr
      .replace(/,\s*}/g, '}')  // 移除对象末尾的逗号
      .replace(/,\s*]/g, ']')  // 移除数组末尾的逗号
      .replace(/'/g, '"')      // 替换单引号为双引号
      .replace(/\n/g, ' ')     // 移除换行符
      .replace(/\s+/g, ' ')    // 合并多个空格
      .trim();

    // 尝试多次解析，逐步修复JSON
    let parsed;
    try {
      parsed = JSON.parse(jsonStr);
    } catch (firstError) {
      console.log('First JSON parse failed, trying to fix...', firstError.message);

      // 尝试修复更多问题
      try {
        // 移除可能的尾随逗号和其他问题
        let fixedJson = jsonStr
          .replace(/,(\s*[}\]])/g, '$1')  // 移除尾随逗号
          .replace(/([{,]\s*)(\w+):/g, '$1"$2":')  // 给属性名加引号
          .replace(/:\s*([^",\[\]{}\s]+)(\s*[,}\]])/g, ':"$1"$2'); // 给字符串值加引号

        parsed = JSON.parse(fixedJson);
      } catch (secondError) {
        console.log('Second JSON parse failed, trying simpler approach...', secondError.message);

        // 如果还是失败，尝试提取部分有效的JSON
        try {
          // 尝试只解析concepts部分
          const conceptsMatch = jsonStr.match(/"concepts"\s*:\s*\[([\s\S]*?)\]/);
          if (conceptsMatch) {
            const conceptsStr = `{"concepts":[${conceptsMatch[1]}]}`;
            const conceptsFixed = conceptsStr.replace(/,(\s*[}\]])/g, '$1');
            const conceptsData = JSON.parse(conceptsFixed);

            parsed = {
              topic: topic,
              concepts: conceptsData.concepts || [],
              relationships: [],
              insights: [`AI分析了${topic}的核心概念`],
              suggestions: [`深入研究${topic}的相关理论`]
            };
          } else {
            throw new Error('Cannot extract valid JSON');
          }
        } catch (thirdError) {
          console.log('All JSON parsing attempts failed:', thirdError.message);
          throw new Error('Failed to parse AI response after multiple attempts');
        }
      }
    }

    // 转换为我们的数据结构
    const nodes: ConceptNode[] = parsed.concepts?.map((concept: any, index: number) => ({
      id: generateId(concept.name),
      name: concept.name,
      description: concept.description || `${concept.name}是${topic}中的重要概念`,
      category: getPhilosophyCategory(topic), // 根据主题确定分类
      importance: concept.importance || Math.max(0.3, 1 - index * 0.1),
      level: concept.level || Math.min(Math.floor(index / 2), 3),
      // 新增详细解析字段
      type: concept.category as 'core' | 'supporting' | 'advanced',
      conceptType: concept.type as '承重墙' | '装饰品' | '暗门',
      essence: concept.essence,
      significance: concept.significance,
      practice: concept.practice,
      pitfalls: concept.pitfalls || [],
      secretTips: concept.secretTips || [],
      metadata: {
        philosophers: concept.philosophers?.map((p: any) => ({
          id: generateId(p.name),
          name: p.name,
          nameEn: '',
          lifespan: '',
          nationality: '',
          school: [],
          mainContributions: [p.contribution || '']
        })) || [],
        works: [],
        historicalPeriod: getHistoricalPeriod(topic),
        schools: [topic],
        keywords: [concept.name, topic],
        difficulty: getDifficultyLevel(concept.category, concept.level),
        popularity: concept.importance || Math.max(0.3, 1 - index * 0.1)
      }
    })) || [];

    // 创建名称到ID的映射
    const nameToId = new Map<string, string>();
    nodes.forEach(node => {
      nameToId.set(node.name, node.id);
    });

    const edges: ConceptEdge[] = parsed.relationships?.map((rel: any) => ({
      id: generateId(`${rel.source}-${rel.target}`),
      source: nameToId.get(rel.source) || generateId(rel.source),
      target: nameToId.get(rel.target) || generateId(rel.target),
      type: rel.type === 'contains' ? RelationType.CONTAINS :
            rel.type === 'influences' ? RelationType.INFLUENCES :
            rel.type === 'opposes' ? RelationType.OPPOSES :
            RelationType.RELATES,
      strength: rel.strength || 0.5,
      description: rel.description || '',
      bidirectional: false
    })) || [];

    const graph: KnowledgeGraph = {
      id: generateId(`${topic}-ai-graph`),
      topic,
      description: `AI生成的${topic}知识图谱`,
      nodes,
      edges,
      metadata: {
        totalConcepts: nodes.length,
        maxDepth: Math.max(...nodes.map(n => n.level)),
        categories: [...new Set(nodes.map(n => n.category))],
        complexity: Math.min(nodes.length, 10),
        completeness: 0.9,
        sources: ['AI生成', 'OpenRouter API']
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return {
      graph,
      insights: parsed.insights || [`AI分析了${topic}的核心概念和关系`],
      suggestions: parsed.suggestions || [`深入研究${topic}的相关著作`],
      confidence: 0.85,
      // 新增详细解析字段
      coreInsight: parsed.coreInsight,
      conceptMap: parsed.conceptMap,
      actionGuide: parsed.actionGuide,
      mentorMessage: parsed.mentorMessage
    };

  } catch (error) {
    console.error('Failed to parse AI response:', error);
    return null;
  }
}

// 辅助函数
function generateId(name: string): string {
  return name.toLowerCase().replace(/[^a-z0-9\u4e00-\u9fa5]/g, '-') + '-' + Math.random().toString(36).substr(2, 9);
}

function getPhilosophyCategory(topic: string): PhilosophyCategory {
  const categoryMap: { [key: string]: PhilosophyCategory } = {
    '存在主义': PhilosophyCategory.EXISTENTIALISM,
    '道德哲学': PhilosophyCategory.ETHICS,
    '伦理学': PhilosophyCategory.ETHICS,
    '认识论': PhilosophyCategory.EPISTEMOLOGY,
    '形而上学': PhilosophyCategory.METAPHYSICS,
    '心灵哲学': PhilosophyCategory.PHILOSOPHY_OF_MIND,
    '政治哲学': PhilosophyCategory.POLITICAL_PHILOSOPHY,
    '逻辑学': PhilosophyCategory.LOGIC,
    '美学': PhilosophyCategory.AESTHETICS,
    '宗教哲学': PhilosophyCategory.PHILOSOPHY_OF_RELIGION,
    '现象学': PhilosophyCategory.PHENOMENOLOGY,
    '分析哲学': PhilosophyCategory.ANALYTIC_PHILOSOPHY,
    '大陆哲学': PhilosophyCategory.CONTINENTAL_PHILOSOPHY,
    '东方哲学': PhilosophyCategory.EASTERN_PHILOSOPHY,
    '古代哲学': PhilosophyCategory.ANCIENT_PHILOSOPHY,
    '现代哲学': PhilosophyCategory.MODERN_PHILOSOPHY,
    '自由意志': PhilosophyCategory.PHILOSOPHY_OF_MIND
  };

  return categoryMap[topic] || PhilosophyCategory.CONTEMPORARY_PHILOSOPHY;
}

function getHistoricalPeriod(topic: string): string {
  const periodMap: { [key: string]: string } = {
    '存在主义': '20世纪',
    '道德哲学': '古代至现代',
    '认识论': '古代至现代',
    '形而上学': '古代至现代',
    '心灵哲学': '现代',
    '政治哲学': '古代至现代',
    '逻辑学': '古代至现代',
    '美学': '18-19世纪',
    '现象学': '20世纪',
    '分析哲学': '20世纪',
    '自由意志': '古代至现代'
  };

  return periodMap[topic] || '现代';
}

function getDifficultyLevel(category: string, level: number): number {
  const baseDifficulty: { [key: string]: number } = {
    'core': 2,
    'supporting': 3,
    'advanced': 4
  };

  return Math.min((baseDifficulty[category] || 3) + level, 5);
}

function getFallbackConcepts(topic: string, depth: number): string[] {
  const conceptMap: { [key: string]: string[] } = {
    '存在主义': ['自由选择', '焦虑', '真实性', '坏信仰', '荒诞', '责任', '此在'],
    '道德哲学': ['功利主义', '义务论', '德性伦理', '道德相对主义', '道德实在论', '最大幸福原则'],
    '自由意志': ['决定论', '非决定论', '相容论', '道德责任', '因果决定论', '选择自由'],
    '认识论': ['知识', '信念', '真理', '怀疑主义', '经验主义', '理性主义', '先验知识'],
    '心灵哲学': ['意识', '心身问题', '物理主义', '二元论', '功能主义', '意向性'],
    '政治哲学': ['正义', '自由', '平等', '权威', '社会契约', '民主', '权利'],
    '美学': ['美', '艺术', '审美经验', '美的判断', '艺术本质', '审美态度']
  };

  const concepts = conceptMap[topic] || ['概念1', '概念2', '概念3', '概念4'];
  return concepts.slice(0, depth * 2);
}

function getRelatedPhilosophers(concept: string, topic: string): any[] {
  const philosopherMap: { [key: string]: any[] } = {
    '自由选择': [{ name: '让-保罗·萨特', contribution: '存在先于本质理论' }],
    '焦虑': [{ name: '索伦·克尔凯郭尔', contribution: '焦虑概念的先驱' }],
    '功利主义': [{ name: '约翰·斯图尔特·密尔', contribution: '功利主义发展' }],
    '义务论': [{ name: '伊曼努尔·康德', contribution: '绝对命令理论' }],
    '决定论': [{ name: '皮埃尔-西蒙·拉普拉斯', contribution: '科学决定论' }],
    '意识': [{ name: '大卫·查默斯', contribution: '意识的困难问题' }]
  };

  return philosopherMap[concept] || [];
}

function isValidPhilosophyConcept(concept: string): boolean {
  // 过滤掉无效的概念
  const invalidPatterns = [
    /^(concepts?|name|description|importance|level|category|essence|significance)$/i,
    /^[""'`\s]*$/,
    /^[\d\-\*\+\s]*$/,
    /^(的|是|在|有|和|与|或|但|而|等|及|以|为|由|从|到|对|关于|我|你|他|她|它|这|那|将|为你|好的)$/,
    /^(json|array|object|string|number|boolean|null|undefined)$/i,
    /^(请|让我|我将|现在|接下来|首先|然后|最后|总结|结论)$/,
    /绘制|图谱|知识|地图|结构|版本|格式/,
    /^.{0,1}$|^.{25,}$/, // 太短或太长
    /^\s*[""'`]+.*[""'`]+\s*$/, // 被引号包围的
    /^[a-zA-Z\s]*\([^)]*\)\s*$/, // 只有英文和括号的
  ];

  // 检查是否匹配无效模式
  for (const pattern of invalidPatterns) {
    if (pattern.test(concept)) {
      return false;
    }
  }

  // 检查长度
  if (concept.length < 2 || concept.length > 25) {
    return false;
  }

  // 必须包含中文字符或者是常见的哲学术语
  const hasChineseChars = /[\u4e00-\u9fa5]/.test(concept);
  const isPhilosophyTerm = /^(存在|自由|意识|真理|正义|美|善|恶|道德|伦理|逻辑|理性|感性|经验|先验|本质|现象|实在|虚无|时间|空间|因果|必然|偶然|可能|现实|理想|精神|物质|主体|客体|绝对|相对|有限|无限|一|多|同|异|运动|静止|变化|永恒|生|死|爱|恨|希望|绝望|焦虑|恐惧|勇气|智慧|知识|信念|怀疑|确定|不确定|真|假|对|错|应该|不应该|权利|义务|责任|自由意志|决定论|目的|手段|价值|意义|荒诞|理想|现实|个体|社会|国家|政治|法律|宗教|艺术|科学|技术|文化|历史|传统|现代|后现代|东方|西方|古代|中世纪|近代|当代|哲学|思想|观念|概念|范畴|原理|原则|方法|体系|学说|理论|主义|派别|流派|学校)/.test(concept);

  return hasChineseChars || isPhilosophyTerm;
}

function generateConceptDescription(concept: string, topic: string): string {
  const descriptions: { [key: string]: string } = {
    '存在先于本质': '萨特提出的核心理念，认为人首先存在，然后通过选择和行动创造自己的本质',
    '自由选择': '存在主义强调的人类根本特征，认为人在任何情况下都有选择的自由',
    '焦虑': '面对自由和责任时产生的根本性情感体验',
    '真实性': '按照自己的本真存在方式生活，不受外在期望和社会角色束缚',
    '坏信仰': '个体为逃避自由和责任而采取的自欺行为',
    '荒诞': '人类寻求意义的努力与世界无意义本质之间的根本冲突',
    '责任': '伴随自由选择而来的对自己行为后果的承担'
  };
  
  return descriptions[concept] || `${concept}是${topic}中的重要概念`;
}

function getRandomRelationType(): RelationType {
  const types = [
    RelationType.INFLUENCES,
    RelationType.RELATES,
    RelationType.DEVELOPS,
    RelationType.APPLIES,
    RelationType.DERIVES
  ];
  return types[Math.floor(Math.random() * types.length)];
}

function generateInsights(topic: string, nodeCount: number, category: PhilosophyCategory): string[] {
  return [
    `${topic}包含${nodeCount}个核心概念，形成了复杂的理论体系`,
    `该主题属于${category}范畴，具有深刻的哲学意义`,
    `概念间的相互关系展现了${topic}的内在逻辑结构`,
    `这些概念在现代哲学讨论中仍然具有重要价值`
  ];
}

function generateSuggestions(topic: string, depth: number): string[] {
  const suggestions = [
    `深入研究${topic}的历史发展脉络`,
    `探索${topic}与其他哲学分支的关系`,
    `阅读相关的经典哲学著作`,
    `思考${topic}在当代社会中的应用价值`
  ];
  
  if (depth < 3) {
    suggestions.push(`增加分析深度以获得更全面的理解`);
  }
  
  return suggestions;
}
