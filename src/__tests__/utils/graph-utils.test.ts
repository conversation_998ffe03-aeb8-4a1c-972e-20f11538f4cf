import {
  calculateNodeSize,
  getCategoryColor,
  getEdgeStyle,
  searchNodes,
  validateGraph,
  calculateGraphStats
} from '@/utils/graph-utils';
import { PhilosophyCategory, RelationType, KnowledgeGraph } from '@/types/knowledge-graph';
import { sampleKnowledgeGraph } from '@/data/sample-data';

describe('Graph Utils', () => {
  describe('calculateNodeSize', () => {
    it('should calculate node size based on importance', () => {
      expect(calculateNodeSize(0)).toBe(8);
      expect(calculateNodeSize(1)).toBe(24);
      expect(calculateNodeSize(0.5)).toBe(16);
    });

    it('should respect custom min and max sizes', () => {
      expect(calculateNodeSize(0, 10, 30)).toBe(10);
      expect(calculateNodeSize(1, 10, 30)).toBe(30);
      expect(calculateNodeSize(0.5, 10, 30)).toBe(20);
    });
  });

  describe('getCategoryColor', () => {
    it('should return correct colors for different categories', () => {
      expect(getCategoryColor(PhilosophyCategory.EXISTENTIALISM)).toBe('#1F2937');
      expect(getCategoryColor(PhilosophyCategory.ETHICS)).toBe('#EF4444');
      expect(getCategoryColor(PhilosophyCategory.LOGIC)).toBe('#10B981');
    });

    it('should return default color for unknown category', () => {
      const unknownCategory = 'unknown' as PhilosophyCategory;
      expect(getCategoryColor(unknownCategory)).toBe('#6B7280');
    });
  });

  describe('getEdgeStyle', () => {
    it('should return correct styles for different relation types', () => {
      const containsStyle = getEdgeStyle(RelationType.CONTAINS);
      expect(containsStyle.color).toBe('#374151');
      expect(containsStyle.width).toBe(2);

      const opposesStyle = getEdgeStyle(RelationType.OPPOSES);
      expect(opposesStyle.color).toBe('#EF4444');
      expect(opposesStyle.dashArray).toBe('5,5');
    });
  });

  describe('searchNodes', () => {
    it('should filter nodes by search query', () => {
      const result = searchNodes(sampleKnowledgeGraph, '存在');
      expect(result.nodes.length).toBeGreaterThan(0);
      expect(result.nodes.every(node => 
        node.name.includes('存在') || 
        node.description.includes('存在') ||
        node.metadata.keywords.some(keyword => keyword.includes('存在'))
      )).toBe(true);
    });

    it('should filter nodes by category', () => {
      const result = searchNodes(sampleKnowledgeGraph, '', {
        categories: [PhilosophyCategory.EXISTENTIALISM]
      });
      expect(result.nodes.every(node => 
        node.category === PhilosophyCategory.EXISTENTIALISM
      )).toBe(true);
    });

    it('should return all nodes when no filters applied', () => {
      const result = searchNodes(sampleKnowledgeGraph, '');
      expect(result.nodes.length).toBe(sampleKnowledgeGraph.nodes.length);
    });
  });

  describe('validateGraph', () => {
    it('should validate a correct graph', () => {
      const result = validateGraph(sampleKnowledgeGraph);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect duplicate node IDs', () => {
      const invalidGraph: KnowledgeGraph = {
        ...sampleKnowledgeGraph,
        nodes: [
          ...sampleKnowledgeGraph.nodes,
          { ...sampleKnowledgeGraph.nodes[0] } // 重复节点
        ]
      };
      
      const result = validateGraph(invalidGraph);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('存在重复的节点ID');
    });

    it('should detect invalid edge references', () => {
      const invalidGraph: KnowledgeGraph = {
        ...sampleKnowledgeGraph,
        edges: [
          ...sampleKnowledgeGraph.edges,
          {
            id: 'invalid-edge',
            source: 'non-existent-node',
            target: 'another-non-existent-node',
            type: RelationType.RELATES,
            strength: 0.5,
            bidirectional: false
          }
        ]
      };
      
      const result = validateGraph(invalidGraph);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('calculateGraphStats', () => {
    it('should calculate correct statistics', () => {
      const stats = calculateGraphStats(sampleKnowledgeGraph);
      
      expect(stats.totalNodes).toBe(sampleKnowledgeGraph.nodes.length);
      expect(stats.totalEdges).toBe(sampleKnowledgeGraph.edges.length);
      expect(stats.maxDepth).toBeGreaterThanOrEqual(0);
      expect(stats.avgImportance).toBeGreaterThan(0);
      expect(stats.avgImportance).toBeLessThanOrEqual(1);
      expect(stats.density).toBeGreaterThanOrEqual(0);
      expect(stats.density).toBeLessThanOrEqual(1);
    });
  });
});
