import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import SearchInterface from '@/components/SearchInterface';
import { AnalysisRequest } from '@/types/knowledge-graph';

describe('SearchInterface', () => {
  const mockOnSearch = jest.fn();

  beforeEach(() => {
    mockOnSearch.mockClear();
  });

  it('should render search interface correctly', () => {
    render(<SearchInterface onSearch={mockOnSearch} isLoading={false} />);
    
    expect(screen.getByText('AI 哲学概念分析')).toBeInTheDocument();
    expect(screen.getByLabelText('哲学主题')).toBeInTheDocument();
    expect(screen.getByText('生成知识图谱')).toBeInTheDocument();
  });

  it('should display popular topics', () => {
    render(<SearchInterface onSearch={mockOnSearch} isLoading={false} />);
    
    expect(screen.getByText('存在主义')).toBeInTheDocument();
    expect(screen.getByText('道德哲学')).toBeInTheDocument();
    expect(screen.getByText('自由意志')).toBeInTheDocument();
  });

  it('should handle topic input', () => {
    render(<SearchInterface onSearch={mockOnSearch} isLoading={false} />);
    
    const input = screen.getByLabelText('哲学主题');
    fireEvent.change(input, { target: { value: '存在主义' } });
    
    expect(input).toHaveValue('存在主义');
  });

  it('should handle popular topic click', () => {
    render(<SearchInterface onSearch={mockOnSearch} isLoading={false} />);
    
    const input = screen.getByLabelText('哲学主题');
    const existentialismButton = screen.getByText('存在主义');
    
    fireEvent.click(existentialismButton);
    
    expect(input).toHaveValue('存在主义');
  });

  it('should handle depth slider change', () => {
    render(<SearchInterface onSearch={mockOnSearch} isLoading={false} />);
    
    const slider = screen.getByLabelText(/分析深度/);
    fireEvent.change(slider, { target: { value: '4' } });
    
    expect(screen.getByText('分析深度: 4')).toBeInTheDocument();
  });

  it('should submit form with correct data', async () => {
    render(<SearchInterface onSearch={mockOnSearch} isLoading={false} />);
    
    const input = screen.getByLabelText('哲学主题');
    const submitButton = screen.getByText('生成知识图谱');
    
    fireEvent.change(input, { target: { value: '存在主义' } });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockOnSearch).toHaveBeenCalledWith({
        topic: '存在主义',
        depth: 3,
        language: 'zh',
        includeHistorical: true,
        includeContemporary: true
      });
    });
  });

  it('should disable submit button when topic is empty', () => {
    render(<SearchInterface onSearch={mockOnSearch} isLoading={false} />);

    const submitButton = screen.getByRole('button', { name: /生成知识图谱|生成图谱/ });
    expect(submitButton).toBeDisabled();
  });

  it('should disable form when loading', () => {
    render(<SearchInterface onSearch={mockOnSearch} isLoading={true} />);

    const input = screen.getByLabelText('哲学主题');
    const submitButton = screen.getByRole('button', { name: /分析中/ });

    expect(input).toBeDisabled();
    expect(submitButton).toBeDisabled();
  });

  it('should show advanced options when clicked', () => {
    render(<SearchInterface onSearch={mockOnSearch} isLoading={false} />);
    
    const advancedButton = screen.getByText('高级选项');
    fireEvent.click(advancedButton);
    
    expect(screen.getByText('关注分类 (可选)')).toBeInTheDocument();
    expect(screen.getByText('包含历史背景')).toBeInTheDocument();
  });

  it('should handle category selection in advanced options', () => {
    render(<SearchInterface onSearch={mockOnSearch} isLoading={false} />);
    
    // 打开高级选项
    const advancedButton = screen.getByText('高级选项');
    fireEvent.click(advancedButton);
    
    // 选择存在主义分类
    const existentialismCheckbox = screen.getByLabelText('存在主义');
    fireEvent.click(existentialismCheckbox);
    
    expect(existentialismCheckbox).toBeChecked();
  });

  it('should prevent form submission with empty topic', () => {
    render(<SearchInterface onSearch={mockOnSearch} isLoading={false} />);

    const submitButton = screen.getByRole('button', { name: /生成知识图谱|生成图谱/ });
    fireEvent.click(submitButton);

    expect(mockOnSearch).not.toHaveBeenCalled();
  });
});
