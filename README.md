# 哲学知识图谱学习网站

一个基于AI的交互式哲学概念学习平台，通过可视化知识图谱帮助用户探索哲学概念之间的内在联系。

## 🌟 核心功能

- **AI概念分析**: 智能分析哲学主题，自动提取核心概念和关系
- **交互式图谱**: 动态可视化知识图谱，支持缩放、拖拽、节点点击
- **概念详情**: 点击节点查看详细解释、相关哲学家、经典著作
- **移动端友好**: 响应式设计，优化触摸交互体验
- **多层级分析**: 支持1-5级分析深度，满足不同学习需求

## 🚀 技术栈

- **前端框架**: Next.js 15 + React 19
- **类型系统**: TypeScript
- **样式框架**: Tailwind CSS
- **数据可视化**: D3.js
- **图标库**: Lucide React
- **动画库**: Framer Motion
- **测试框架**: Jest + React Testing Library

## 📦 快速开始

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本

```bash
npm run build
npm start
```

### 运行测试

```bash
npm test
```

## 🎯 使用指南

1. **输入主题**: 在搜索框中输入哲学主题（如"存在主义"、"道德哲学"）
2. **调整设置**: 选择分析深度和其他选项
3. **生成图谱**: 点击"生成知识图谱"按钮
4. **探索概念**: 点击节点查看详细信息，拖拽调整布局

## 🚀 部署

推荐使用 [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) 部署。

查看 [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) 了解更多部署选项。
