<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>详细概念解析测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .success {
            border-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <h1>🧠 哲学知识图谱 - 详细概念解析测试</h1>
    
    <div class="test-section">
        <h2>📋 测试说明</h2>
        <p>这个页面用于测试新的详细概念解析功能。我们将测试以下几个方面：</p>
        <ul>
            <li>✅ AI分析API是否返回详细解析数据</li>
            <li>✅ 概念节点是否包含新的解析字段</li>
            <li>✅ 详细解析组件是否正常显示</li>
            <li>✅ 工作流程格式是否正确应用</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🔬 API测试</h2>
        <p>测试不同哲学主题的AI分析结果：</p>
        
        <button class="test-button" onclick="testTopic('存在主义')">测试：存在主义</button>
        <button class="test-button" onclick="testTopic('自由意志')">测试：自由意志</button>
        <button class="test-button" onclick="testTopic('道德哲学')">测试：道德哲学</button>
        <button class="test-button" onclick="testTopic('心灵哲学')">测试：心灵哲学</button>
        
        <div id="api-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>📊 测试结果统计</h2>
        <div id="test-stats">
            <p>总测试次数: <span id="total-tests">0</span></p>
            <p>成功次数: <span id="success-tests">0</span></p>
            <p>失败次数: <span id="failed-tests">0</span></p>
            <p>成功率: <span id="success-rate">0%</span></p>
        </div>
    </div>

    <div class="test-section">
        <h2>🎯 功能验证清单</h2>
        <div id="feature-checklist">
            <div>
                <input type="checkbox" id="check-core-insight" disabled>
                <label for="check-core-insight">核心洞察 (coreInsight) 字段</label>
            </div>
            <div>
                <input type="checkbox" id="check-concept-map" disabled>
                <label for="check-concept-map">概念地图 (conceptMap) 字段</label>
            </div>
            <div>
                <input type="checkbox" id="check-action-guide" disabled>
                <label for="check-action-guide">行动指南 (actionGuide) 字段</label>
            </div>
            <div>
                <input type="checkbox" id="check-mentor-message" disabled>
                <label for="check-mentor-message">导师寄语 (mentorMessage) 字段</label>
            </div>
            <div>
                <input type="checkbox" id="check-concept-types" disabled>
                <label for="check-concept-types">概念分类 (承重墙/装饰品/暗门)</label>
            </div>
            <div>
                <input type="checkbox" id="check-detailed-fields" disabled>
                <label for="check-detailed-fields">详细解析字段 (essence, significance, practice)</label>
            </div>
        </div>
    </div>

    <script>
        let totalTests = 0;
        let successTests = 0;
        let failedTests = 0;

        function updateStats() {
            document.getElementById('total-tests').textContent = totalTests;
            document.getElementById('success-tests').textContent = successTests;
            document.getElementById('failed-tests').textContent = failedTests;
            document.getElementById('success-rate').textContent = 
                totalTests > 0 ? Math.round((successTests / totalTests) * 100) + '%' : '0%';
        }

        function checkFeature(featureId, condition) {
            const checkbox = document.getElementById(featureId);
            if (condition) {
                checkbox.checked = true;
                checkbox.style.accentColor = '#28a745';
            }
        }

        async function testTopic(topic) {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = `正在测试主题: ${topic}...`;

            totalTests++;
            updateStats();

            try {
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        topic: topic,
                        depth: 3,
                        language: 'zh',
                        includeHistorical: true,
                        includeContemporary: true
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                
                if (result.success) {
                    successTests++;
                    resultDiv.className = 'result success';
                    
                    // 验证新功能
                    checkFeature('check-core-insight', !!result.coreInsight);
                    checkFeature('check-concept-map', !!result.conceptMap);
                    checkFeature('check-action-guide', !!result.actionGuide);
                    checkFeature('check-mentor-message', !!result.mentorMessage);
                    
                    // 检查概念节点的详细字段
                    const hasConceptTypes = result.graph.nodes.some(node => 
                        node.conceptType && ['承重墙', '装饰品', '暗门'].includes(node.conceptType)
                    );
                    checkFeature('check-concept-types', hasConceptTypes);
                    
                    const hasDetailedFields = result.graph.nodes.some(node => 
                        node.essence || node.significance || node.practice
                    );
                    checkFeature('check-detailed-fields', hasDetailedFields);
                    
                    resultDiv.textContent = `✅ 测试成功！
主题: ${topic}
概念数量: ${result.graph.nodes.length}
关系数量: ${result.graph.edges.length}
置信度: ${Math.round(result.confidence * 100)}%
处理时间: ${result.processingTime}ms

🔍 详细解析数据:
- 核心洞察: ${result.coreInsight ? '✅' : '❌'}
- 概念地图: ${result.conceptMap ? '✅' : '❌'}
- 行动指南: ${result.actionGuide ? '✅' : '❌'}
- 导师寄语: ${result.mentorMessage ? '✅' : '❌'}

📊 概念分析:
- 承重墙概念: ${result.graph.nodes.filter(n => n.conceptType === '承重墙').length}
- 装饰品概念: ${result.graph.nodes.filter(n => n.conceptType === '装饰品').length}
- 暗门概念: ${result.graph.nodes.filter(n => n.conceptType === '暗门').length}

💡 洞察示例:
${result.insights.slice(0, 2).join('\n')}`;
                } else {
                    throw new Error(result.error || '分析失败');
                }
            } catch (error) {
                failedTests++;
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 测试失败: ${error.message}`;
            }

            updateStats();
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 详细概念解析测试页面已加载');
            console.log('📝 请点击测试按钮开始验证新功能');
        });
    </script>
</body>
</html>
