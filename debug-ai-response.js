// 调试AI响应格式的脚本
const fetch = require('node-fetch');

async function testAIResponse() {
  try {
    console.log('🧪 Testing AI response format...');
    
    const response = await fetch('http://localhost:3000/api/analyze', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        topic: '存在主义',
        depth: 3,
        language: 'zh',
        includeHistorical: true,
        includeContemporary: true
      })
    });

    const result = await response.json();
    
    console.log('📊 Response structure:');
    console.log('- Success:', result.success);
    console.log('- Graph nodes count:', result.graph?.nodes?.length || 0);
    console.log('- Has coreInsight:', !!result.coreInsight);
    console.log('- Has conceptMap:', !!result.conceptMap);
    console.log('- Has actionGuide:', !!result.actionGuide);
    console.log('- Has mentorMessage:', !!result.mentorMessage);
    
    if (result.graph?.nodes?.length > 0) {
      const firstNode = result.graph.nodes[0];
      console.log('\n🔍 First node analysis:');
      console.log('- Name:', firstNode.name);
      console.log('- ConceptType:', firstNode.conceptType);
      console.log('- Has essence:', !!firstNode.essence);
      console.log('- Has significance:', !!firstNode.significance);
      console.log('- Has practice:', !!firstNode.practice);
      console.log('- Has pitfalls:', !!firstNode.pitfalls);
      console.log('- Has secretTips:', !!firstNode.secretTips);
    }
    
    console.log('\n✅ Test completed');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testAIResponse();
